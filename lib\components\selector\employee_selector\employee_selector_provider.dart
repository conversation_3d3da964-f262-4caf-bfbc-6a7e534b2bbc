import 'package:flutter/material.dart';

/// 部门选择器状态管理 Provider
class EmployeeSelectorProvider extends ChangeNotifier {
  /// 当前选中人员列表
  List<dynamic> _checkedEmployees = [];

  /// 获取当前选中的部门列表
  List<dynamic> get checkedEmployees => List.unmodifiable(_checkedEmployees);

  /// 默认选中的人员ID列表
  List<String> _defaultCheckedEmployeeIds = [];

  /// 设置默认选中的人员ID列表
  void setDefaultCheckedEmployeeIds(List<String> departmentIds) {
    _defaultCheckedEmployeeIds = List.from(departmentIds);
    notifyListeners();
  }
}
